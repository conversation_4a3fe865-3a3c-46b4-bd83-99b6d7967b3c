<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BudgetBuddy - Personal Expense Tracker</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="app-container">
        <!-- Sidebar Navigation -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-wallet"></i>
                    <h1>BudgetBuddy</h1>
                </div>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li class="nav-item active">
                        <a href="#" class="nav-link">
                            <i class="fas fa-chart-pie"></i>
                sync            <span>Dashboard</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-tags"></i>
                            <span>Categories</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-chart-bar"></i>
                            <span>Reports</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-plus-circle"></i>
                            <span>Add Transaction</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-cog"></i>
                            <span>Settings</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content Area -->
        <main class="main-content">
            <!-- Header -->
            <header class="header">
                <div class="header-left">
                    <button class="mobile-menu-btn" onclick="toggleSidebar()" aria-label="Toggle navigation menu">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="header-text">
                        <h2>Dashboard</h2>
                        <p class="header-subtitle">Welcome back! Here's your financial overview</p>
                    </div>
                </div>
                <div class="header-right">
                    <div class="balance-display">
                        <span class="balance-label">Total Balance</span>
                        <span class="balance-amount">₹15,000</span>
                    </div>
                    <div class="profile">
                        <img src="https://via.placeholder.com/40x40/4f46e5/ffffff?text=U" alt="User Profile" class="profile-image">
                        <span class="profile-name">John Doe</span>
                    </div>
                </div>
            </header>

            <!-- Overview Cards -->
            <section class="overview-cards">
                <div class="card income-card">
                    <div class="card-icon">
                        <i class="fas fa-arrow-up"></i>
                    </div>
                    <div class="card-content">
                        <h3>Total Income</h3>
                        <p class="card-amount">₹25,000</p>
                        <span class="card-change positive">+12% from last month</span>
                    </div>
                </div>

                <div class="card expense-card">
                    <div class="card-icon">
                        <i class="fas fa-arrow-down"></i>
                    </div>
                    <div class="card-content">
                        <h3>Total Spent</h3>
                        <p class="card-amount">₹10,000</p>
                        <span class="card-change negative">+8% from last month</span>
                    </div>
                </div>

                <div class="card savings-card">
                    <div class="card-icon">
                        <i class="fas fa-piggy-bank"></i>
                    </div>
                    <div class="card-content">
                        <h3>Savings Rate</h3>
                        <p class="card-amount">60%</p>
                        <span class="card-change positive">+5% from last month</span>
                    </div>
                </div>

                <div class="card category-card">
                    <div class="card-icon">
                        <i class="fas fa-utensils"></i>
                    </div>
                    <div class="card-content">
                        <h3>Top Category</h3>
                        <p class="card-amount">Food</p>
                        <span class="card-change">₹3,500 this month</span>
                    </div>
                </div>
            </section>

            <!-- Content Grid -->
            <div class="content-grid">
                <!-- Recent Transactions -->
                <section class="transactions-section">
                    <div class="section-header">
                        <h3>Recent Transactions</h3>
                        <a href="#" class="view-all">View All</a>
                    </div>
                    
                    <div class="table-container">
                        <table class="transactions-table">
                            <thead>
                                <tr>
                                    <th scope="col">Date</th>
                                    <th scope="col">Description</th>
                                    <th scope="col">Category</th>
                                    <th scope="col">Amount</th>
                                    <th scope="col">Type</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>July 31</td>
                                    <td>Grocery Shopping</td>
                                    <td>
                                        <span class="category-tag food">
                                            <i class="fas fa-utensils"></i>
                                            Food
                                        </span>
                                    </td>
                                    <td class="amount expense">-₹1,200</td>
                                    <td><span class="type-badge expense">Expense</span></td>
                                </tr>
                                <tr>
                                    <td>July 30</td>
                                    <td>Salary Credit</td>
                                    <td>
                                        <span class="category-tag income">
                                            <i class="fas fa-briefcase"></i>
                                            Income
                                        </span>
                                    </td>
                                    <td class="amount income">+₹25,000</td>
                                    <td><span class="type-badge income">Income</span></td>
                                </tr>
                                <tr>
                                    <td>July 29</td>
                                    <td>Movie Tickets</td>
                                    <td>
                                        <span class="category-tag entertainment">
                                            <i class="fas fa-film"></i>
                                            Entertainment
                                        </span>
                                    </td>
                                    <td class="amount expense">-₹800</td>
                                    <td><span class="type-badge expense">Expense</span></td>
                                </tr>
                                <tr>
                                    <td>July 28</td>
                                    <td>Gas Station</td>
                                    <td>
                                        <span class="category-tag transport">
                                            <i class="fas fa-car"></i>
                                            Transport
                                        </span>
                                    </td>
                                    <td class="amount expense">-₹2,000</td>
                                    <td><span class="type-badge expense">Expense</span></td>
                                </tr>
                                <tr>
                                    <td>July 27</td>
                                    <td>Online Shopping</td>
                                    <td>
                                        <span class="category-tag shopping">
                                            <i class="fas fa-shopping-bag"></i>
                                            Shopping
                                        </span>
                                    </td>
                                    <td class="amount expense">-₹1,500</td>
                                    <td><span class="type-badge expense">Expense</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </section>

                <!-- Spending Breakdown -->
                <section class="spending-breakdown">
                    <div class="section-header">
                        <h3>Spending Breakdown</h3>
                        <span class="period">This Month</span>
                    </div>
                    
                    <div class="breakdown-list">
                        <div class="breakdown-item">
                            <div class="breakdown-info">
                                <span class="category-icon food">
                                    <i class="fas fa-utensils"></i>
                                </span>
                                <div class="breakdown-details">
                                    <span class="category-name">Food & Dining</span>
                                    <span class="category-amount">₹3,500</span>
                                </div>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill food" style="width: 35%"></div>
                            </div>
                            <span class="percentage">35%</span>
                        </div>

                        <div class="breakdown-item">
                            <div class="breakdown-info">
                                <span class="category-icon transport">
                                    <i class="fas fa-car"></i>
                                </span>
                                <div class="breakdown-details">
                                    <span class="category-name">Transport</span>
                                    <span class="category-amount">₹2,500</span>
                                </div>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill transport" style="width: 25%"></div>
                            </div>
                            <span class="percentage">25%</span>
                        </div>

                        <div class="breakdown-item">
                            <div class="breakdown-info">
                                <span class="category-icon shopping">
                                    <i class="fas fa-shopping-bag"></i>
                                </span>
                                <div class="breakdown-details">
                                    <span class="category-name">Shopping</span>
                                    <span class="category-amount">₹2,000</span>
                                </div>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill shopping" style="width: 20%"></div>
                            </div>
                            <span class="percentage">20%</span>
                        </div>

                        <div class="breakdown-item">
                            <div class="breakdown-info">
                                <span class="category-icon entertainment">
                                    <i class="fas fa-film"></i>
                                </span>
                                <div class="breakdown-details">
                                    <span class="category-name">Entertainment</span>
                                    <span class="category-amount">₹1,500</span>
                                </div>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill entertainment" style="width: 15%"></div>
                            </div>
                            <span class="percentage">15%</span>
                        </div>

                        <div class="breakdown-item">
                            <div class="breakdown-info">
                                <span class="category-icon utilities">
                                    <i class="fas fa-bolt"></i>
                                </span>
                                <div class="breakdown-details">
                                    <span class="category-name">Utilities</span>
                                    <span class="category-amount">₹500</span>
                                </div>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill utilities" style="width: 5%"></div>
                            </div>
                            <span class="percentage">5%</span>
                        </div>
                    </div>
                </section>
            </div>
        </main>
    </div>

    <script>
        function toggleSidebar() {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('open');
        }

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(event) {
            const sidebar = document.querySelector('.sidebar');
            const menuBtn = document.querySelector('.mobile-menu-btn');

            if (window.innerWidth <= 768 &&
                sidebar.classList.contains('open') &&
                !sidebar.contains(event.target) &&
                !menuBtn.contains(event.target)) {
                sidebar.classList.remove('open');
            }
        });
    </script>
</body>
</html>
