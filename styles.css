/* ===== CSS VARIABLES & DESIGN SYSTEM ===== */
:root {
  /* Colors */
  --primary-color: #4f46e5;
  --primary-light: #6366f1;
  --primary-dark: #3730a3;
  
  /* Status Colors */
  --success-color: #10b981;
  --success-light: #34d399;
  --success-bg: #ecfdf5;
  
  --danger-color: #ef4444;
  --danger-light: #f87171;
  --danger-bg: #fef2f2;
  
  --warning-color: #f59e0b;
  --warning-light: #fbbf24;
  --warning-bg: #fffbeb;
  
  --info-color: #3b82f6;
  --info-light: #60a5fa;
  --info-bg: #eff6ff;
  
  /* Neutral Colors */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  
  /* Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  
  /* Text Colors */
  --text-primary: var(--gray-900);
  --text-secondary: var(--gray-600);
  --text-tertiary: var(--gray-500);
  
  /* Border Colors */
  --border-light: var(--gray-200);
  --border-medium: var(--gray-300);
  --border-dark: var(--gray-400);
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  
  /* Spacing Scale (8px base) */
  --space-1: 0.25rem;  /* 4px */
  --space-2: 0.5rem;   /* 8px */
  --space-3: 0.75rem;  /* 12px */
  --space-4: 1rem;     /* 16px */
  --space-5: 1.25rem;  /* 20px */
  --space-6: 1.5rem;   /* 24px */
  --space-8: 2rem;     /* 32px */
  --space-10: 2.5rem;  /* 40px */
  --space-12: 3rem;    /* 48px */
  --space-16: 4rem;    /* 64px */
  
  /* Typography */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-size-xs: 0.75rem;   /* 12px */
  --font-size-sm: 0.875rem;  /* 14px */
  --font-size-base: 1rem;    /* 16px */
  --font-size-lg: 1.125rem;  /* 18px */
  --font-size-xl: 1.25rem;   /* 20px */
  --font-size-2xl: 1.5rem;   /* 24px */
  --font-size-3xl: 1.875rem; /* 30px */
  --font-size-4xl: 2.25rem;  /* 36px */
  
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* Border Radius */
  --radius-sm: 0.25rem;  /* 4px */
  --radius-md: 0.375rem; /* 6px */
  --radius-lg: 0.5rem;   /* 8px */
  --radius-xl: 0.75rem;  /* 12px */
  --radius-2xl: 1rem;    /* 16px */
  
  /* Layout */
  --sidebar-width: 280px;
  --header-height: 80px;
  
  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

/* ===== RESET & BASE STYLES ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-weight-semibold);
  line-height: 1.3;
  margin-bottom: var(--space-2);
}

h1 { font-size: var(--font-size-3xl); }
h2 { font-size: var(--font-size-2xl); }
h3 { font-size: var(--font-size-xl); }
h4 { font-size: var(--font-size-lg); }
h5 { font-size: var(--font-size-base); }
h6 { font-size: var(--font-size-sm); }

p {
  margin-bottom: var(--space-4);
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--primary-dark);
}

/* ===== UTILITY CLASSES ===== */
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }

.font-light { font-weight: var(--font-weight-light); }
.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }

.text-success { color: var(--success-color); }
.text-danger { color: var(--danger-color); }
.text-warning { color: var(--warning-color); }
.text-info { color: var(--info-color); }

/* ===== LAYOUT ===== */
.app-container {
  display: grid;
  grid-template-columns: var(--sidebar-width) 1fr;
  min-height: 100vh;
}

.main-content {
  display: flex;
  flex-direction: column;
  background-color: var(--bg-secondary);
  overflow-x: hidden;
}

/* ===== SIDEBAR ===== */
.sidebar {
  background-color: var(--bg-primary);
  border-right: 1px solid var(--border-light);
  padding: var(--space-6);
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: var(--sidebar-width);
  overflow-y: auto;
  z-index: 100;
}

.sidebar-header {
  margin-bottom: var(--space-8);
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.logo i {
  font-size: var(--font-size-2xl);
  color: var(--primary-color);
}

.logo h1 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: 0;
}

.sidebar-nav ul {
  list-style: none;
}

.nav-item {
  margin-bottom: var(--space-2);
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-lg);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-fast);
}

.nav-link:hover {
  background-color: var(--gray-50);
  color: var(--text-primary);
}

.nav-item.active .nav-link {
  background-color: var(--primary-color);
  color: white;
}

.nav-link i {
  font-size: var(--font-size-lg);
  width: 20px;
  text-align: center;
}

/* ===== HEADER ===== */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-6) var(--space-8);
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-light);
  height: var(--header-height);
  margin-left: var(--sidebar-width);
  position: sticky;
  top: 0;
  z-index: 50;
}

.header-left h2 {
  margin-bottom: var(--space-1);
}

.header-subtitle {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  margin-bottom: 0;
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--space-6);
}

.balance-display {
  text-align: right;
}

.balance-label {
  display: block;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: var(--space-1);
}

.balance-amount {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--success-color);
}

.profile {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.profile-image {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid var(--border-light);
}

.profile-name {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

/* ===== MAIN CONTENT AREA ===== */
.main-content {
  margin-left: var(--sidebar-width);
  padding: 0;
}

/* ===== OVERVIEW CARDS ===== */
.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-6);
  padding: var(--space-6) var(--space-8);
  margin-bottom: var(--space-6);
}

.card {
  background-color: var(--bg-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  gap: var(--space-4);
  transition: all var(--transition-normal);
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
  color: white;
}

.income-card .card-icon {
  background-color: var(--success-color);
}

.expense-card .card-icon {
  background-color: var(--danger-color);
}

.savings-card .card-icon {
  background-color: var(--warning-color);
}

.category-card .card-icon {
  background-color: var(--info-color);
}

.card-content {
  flex: 1;
}

.card-content h3 {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
  margin-bottom: var(--space-2);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.card-amount {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.card-change {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.card-change.positive {
  color: var(--success-color);
}

.card-change.negative {
  color: var(--danger-color);
}

/* ===== CONTENT GRID ===== */
.content-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--space-8);
  padding: 0 var(--space-8) var(--space-8);
}

/* ===== SECTIONS ===== */
.transactions-section,
.spending-breakdown {
  background-color: var(--bg-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-6);
  padding-bottom: var(--space-4);
  border-bottom: 1px solid var(--border-light);
}

.section-header h3 {
  margin-bottom: 0;
  font-weight: var(--font-weight-semibold);
}

.view-all {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--primary-color);
}

.period {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  background-color: var(--gray-100);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-md);
}

/* ===== TRANSACTIONS TABLE ===== */
.table-container {
  overflow-x: auto;
}

.transactions-table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--font-size-sm);
}

.transactions-table th {
  text-align: left;
  padding: var(--space-3) var(--space-4);
  font-weight: var(--font-weight-semibold);
  color: var(--text-secondary);
  border-bottom: 1px solid var(--border-light);
  font-size: var(--font-size-xs);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.transactions-table td {
  padding: var(--space-4);
  border-bottom: 1px solid var(--gray-100);
  vertical-align: middle;
}

.transactions-table tbody tr:hover {
  background-color: var(--gray-50);
}

.transactions-table tbody tr:last-child td {
  border-bottom: none;
}

.amount {
  font-weight: var(--font-weight-semibold);
  text-align: right;
}

.amount.income {
  color: var(--success-color);
}

.amount.expense {
  color: var(--danger-color);
}

.category-tag {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-md);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.category-tag.food {
  background-color: #fef3c7;
  color: #92400e;
}

.category-tag.income {
  background-color: var(--success-bg);
  color: var(--success-color);
}

.category-tag.entertainment {
  background-color: #fce7f3;
  color: #be185d;
}

.category-tag.transport {
  background-color: #dbeafe;
  color: #1d4ed8;
}

.category-tag.shopping {
  background-color: #f3e8ff;
  color: #7c3aed;
}

.type-badge {
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-md);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.type-badge.income {
  background-color: var(--success-bg);
  color: var(--success-color);
}

.type-badge.expense {
  background-color: var(--danger-bg);
  color: var(--danger-color);
}

/* ===== SPENDING BREAKDOWN ===== */
.breakdown-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-5);
}

.breakdown-item {
  display: grid;
  grid-template-columns: 1fr auto;
  align-items: center;
  gap: var(--space-4);
}

.breakdown-info {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.category-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-base);
  color: white;
}

.category-icon.food {
  background-color: #f59e0b;
}

.category-icon.transport {
  background-color: #3b82f6;
}

.category-icon.shopping {
  background-color: #8b5cf6;
}

.category-icon.entertainment {
  background-color: #ec4899;
}

.category-icon.utilities {
  background-color: #10b981;
}

.breakdown-details {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.category-name {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
}

.category-amount {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
}

.progress-bar {
  width: 120px;
  height: 8px;
  background-color: var(--gray-200);
  border-radius: var(--radius-md);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: var(--radius-md);
  transition: width var(--transition-normal);
}

.progress-fill.food {
  background-color: #f59e0b;
}

.progress-fill.transport {
  background-color: #3b82f6;
}

.progress-fill.shopping {
  background-color: #8b5cf6;
}

.progress-fill.entertainment {
  background-color: #ec4899;
}

.progress-fill.utilities {
  background-color: #10b981;
}

.percentage {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
  min-width: 40px;
  text-align: right;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
  .content-grid {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }

  .spending-breakdown {
    order: -1;
  }
}

@media (max-width: 768px) {
  :root {
    --sidebar-width: 0px;
  }

  .app-container {
    grid-template-columns: 1fr;
  }

  .sidebar {
    transform: translateX(-100%);
    transition: transform var(--transition-normal);
  }

  .sidebar.open {
    transform: translateX(0);
  }

  .main-content {
    margin-left: 0;
  }

  .header {
    margin-left: 0;
    padding: var(--space-4) var(--space-4);
  }

  .header-right {
    gap: var(--space-4);
  }

  .balance-display {
    display: none;
  }

  .overview-cards {
    grid-template-columns: 1fr;
    padding: var(--space-4);
    gap: var(--space-4);
  }

  .content-grid {
    padding: 0 var(--space-4) var(--space-4);
    gap: var(--space-4);
  }

  .transactions-section,
  .spending-breakdown {
    padding: var(--space-4);
  }

  .table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .transactions-table {
    min-width: 600px;
  }

  .breakdown-item {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }

  .breakdown-info {
    justify-content: space-between;
  }

  .progress-bar {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-3);
    height: auto;
    padding: var(--space-4);
  }

  .header-right {
    width: 100%;
    justify-content: space-between;
  }

  .overview-cards {
    padding: var(--space-3);
    gap: var(--space-3);
  }

  .card {
    padding: var(--space-4);
  }

  .card-icon {
    width: 40px;
    height: 40px;
  }

  .content-grid {
    padding: 0 var(--space-3) var(--space-3);
  }

  .transactions-section,
  .spending-breakdown {
    padding: var(--space-3);
  }
}

/* ===== PRINT STYLES ===== */
@media print {
  .sidebar {
    display: none;
  }

  .main-content {
    margin-left: 0;
  }

  .header {
    margin-left: 0;
    box-shadow: none;
    border-bottom: 2px solid var(--border-dark);
  }

  .card {
    box-shadow: none;
    border: 1px solid var(--border-medium);
  }

  .transactions-section,
  .spending-breakdown {
    box-shadow: none;
    border: 1px solid var(--border-medium);
  }
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus styles for keyboard navigation */
.nav-link:focus,
.view-all:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --border-light: var(--gray-400);
    --border-medium: var(--gray-600);
  }
}
